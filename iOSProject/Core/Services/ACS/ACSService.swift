//
//  ACSService.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation
import AzureCommunicationUICalling
import AzureCommunicationCalling
import AzureCommunicationCommon
import os

// MARK: - ACS Service Implementation

/// Simplified Azure Communication Services implementation
class ACSService: ObservableObject {

    // MARK: - Properties

    @Published private(set) var callState: ACSCallState = .idle

    private var callComposite: CallComposite?
    private let connectionString: String
    private let displayName: String
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "iOSProject", category: "ACS")

    // MARK: - Initialization

    init(connectionString: String, displayName: String) {
        self.connectionString = connectionString
        self.displayName = displayName
    }

    /// Convenience initializer using default configuration
    convenience init() {
        let config = ACSConfiguration.default
        self.init(connectionString: config.connectionString, displayName: config.displayName)
    }

    // MARK: - Public Methods

    /// Join a call using a Teams meeting URL or group call ID
    func joinCall(url: String) async throws {
        logger.info("Starting call join process")

        do {
            await MainActor.run {
                callState = .connecting
            }

            // Create call composite
            let composite = try await createCallComposite()

            // Determine locator type based on URL format
            let locator: JoinLocator
            if url.contains("teams.microsoft.com") || url.contains("teams.live.com") {
                locator = .teamsMeeting(teamsLink: url)
                logger.info("Joining Teams meeting")
            } else {
                // Assume it's a group call ID
                guard let uuid = UUID(uuidString: url) else {
                    throw ACSError.callFailed
                }
                locator = .groupCall(groupId: uuid)
                logger.info("Joining group call")
            }

            // Launch the call
            await MainActor.run {
                composite.launch(locator: locator)
                callState = .connected
            }

        } catch {
            await MainActor.run {
                callState = .failed(ACSError.callFailed)
            }
            logger.error("Call join failed: \(error.localizedDescription)")
            throw ACSError.callFailed
        }
    }

    /// Create a new group call and return the group ID
    func createGroupCall() async throws -> String {
        let groupId = UUID().uuidString
        try await joinCall(url: groupId)
        return groupId
    }

    /// End the current call
    func endCall() async {
        await MainActor.run {
            callComposite?.dismiss()
            callComposite = nil
            callState = .disconnected
        }
        logger.info("Call ended")
    }

    /// Check if camera and microphone permissions are granted
    func checkPermissions() async -> Bool {
        let cameraStatus = await checkCameraPermission()
        let microphoneStatus = await checkMicrophonePermission()
        return cameraStatus && microphoneStatus
    }
    
    /// Request camera and microphone permissions
    func requestPermissions() async throws {
        let cameraGranted = await requestCameraPermission()
        let microphoneGranted = await requestMicrophonePermission()

        if !cameraGranted || !microphoneGranted {
            throw ACSError.permissionDenied
        }
    }
    
    // MARK: - Private Methods
    
    private func createCallComposite() async throws -> CallComposite {
        do {
            // For simplified implementation, we'll use a basic token
            // In production, this should be fetched from your backend
            let token = try await generateDevelopmentToken()
            let communicationTokenCredential = try CommunicationTokenCredential(token: token)

            let callCompositeOptions = CallCompositeOptions(displayName: displayName)
            let composite = CallComposite(credential: communicationTokenCredential, withOptions: callCompositeOptions)

            // Set up basic event handlers
            setupEventHandlers(for: composite)

            self.callComposite = composite
            return composite

        } catch {
            await MainActor.run {
                callState = .failed(ACSError.configurationError)
            }
            throw ACSError.configurationError
        }
    }

    private func generateDevelopmentToken() async throws -> String {
        // This is a simplified token generation for development
        // In production, you should call your backend to generate a proper token
        guard !connectionString.isEmpty else {
            throw ACSError.configurationError
        }

        // For now, return a placeholder token
        // This will need to be replaced with actual token generation logic
        return "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjEwNiJ9.placeholder.token"
    }
    
    private func setupEventHandlers(for composite: CallComposite) {
        // Handle call state changes
        composite.events.onCallStateChanged = { [weak self] callStateEvent in
            DispatchQueue.main.async {
                self?.handleCallStateChange(callStateEvent)
            }
        }

        // Handle errors
        composite.events.onError = { [weak self] error in
            DispatchQueue.main.async {
                self?.callState = .failed(ACSError.callFailed)
                self?.logger.error("Call error: \(error.error?.localizedDescription)")
            }
        }

        // Handle dismissal
        composite.events.onDismissed = { [weak self] _ in
            DispatchQueue.main.async {
                self?.callState = .disconnected
                self?.callComposite = nil
            }
        }
    }
    
    private func handleCallStateChange(_ state: AzureCommunicationUICalling.CallState) {
        // Simplified state mapping
        switch state {
        case .connecting:
            callState = .connecting
        case .connected:
            callState = .connected
        case .disconnecting:
            callState = .disconnecting
        case .disconnected:
            callState = .disconnected
        default:
            break
        }
    }
    
    private func checkCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        return status == .authorized
    }

    private func checkMicrophonePermission() async -> Bool {
        let status = AVAudioSession.sharedInstance().recordPermission
        return status == .granted
    }

    private func requestCameraPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                continuation.resume(returning: granted)
            }
        }
    }

    private func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }
}
